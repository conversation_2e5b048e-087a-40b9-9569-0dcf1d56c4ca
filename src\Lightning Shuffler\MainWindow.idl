namespace Lightning_Shuffler
{
    [default_interface]
    runtimeclass MainWindow : Microsoft.UI.Xaml.Window
    {
        MainWindow();
        
        // Properties
        Boolean IsPlaying{ get; set; };
        Int32 LoopCount{ get; set; };
        String CurrentVideoTitle{ get; };
        String SearchQuery{ get; set; };
        
        // Commands
        void PlayPauseCommand();
        void NextVideoCommand();
        void PreviousVideoCommand();
        void ShuffleCommand();
        void ToggleLoopCommand();
        void AddPlaylistCommand(String url);
    }
}