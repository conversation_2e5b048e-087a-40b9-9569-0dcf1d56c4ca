namespace Lightning_Shuffler
{
    runtimeclass VideoItem
    {
        VideoItem();
        String Id{ get; set; };
        String Title{ get; set; };
        String Author{ get; set; };
        String ThumbnailUrl{ get; set; };
        Boolean IsCurrentlyPlaying{ get; set; };
    }

    [default_interface]
    runtimeclass MainWindow : Microsoft.UI.Xaml.Window
    {
        MainWindow();

        // Properties
        Boolean IsPlaying{ get; set; };
        Int32 LoopCount{ get; set; };
        String CurrentVideoTitle{ get; };
        String SearchQuery{ get; set; };
        Windows.Foundation.Collections.IObservableVector<VideoItem> FilteredVideos{ get; };
        Windows.UI.Color VideoAverageColor{ get; };

        // Commands
        void PlayPauseCommand();
        void NextVideoCommand();
        void PreviousVideoCommand();
        void ShuffleCommand();
        void ToggleLoopCommand();
        void AddPlaylistCommand(String url);
    }
}