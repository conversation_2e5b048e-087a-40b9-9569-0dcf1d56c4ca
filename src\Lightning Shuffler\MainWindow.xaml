<?xml version="1.0" encoding="utf-8"?>
<Window
    x:Class="Lightning_Shuffler.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:Lightning_Shuffler"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:media="using:Microsoft.UI.Xaml.Media"
    mc:Ignorable="d"
    Title="Lightning Shuffler">

    <Window.Resources>
        <ResourceDictionary>
            <!-- Colors -->
            <Color x:Key="NeonGreenColor">#39FF14</Color>
            <SolidColorBrush x:Key="NeonGreenBrush"
                    Color="{StaticResource NeonGreenColor}"/>
            <SolidColorBrush x:Key="BackgroundBrush"
                    Color="#FF121212"/>
            <SolidColorBrush x:Key="SurfaceBrush"
                    Color="#FF1E1E1E"/>

            <!-- Animations -->
            <Storyboard x:Key="ShuffleAnimation">
                <DoubleAnimation
                    Storyboard.TargetName="PlaylistListView"
                    Storyboard.TargetProperty="Opacity"
                    From="1.0"
                        To="0.0"
                        Duration="0:0:0.2"/>
                <DoubleAnimation
                    Storyboard.TargetName="PlaylistListView"
                    Storyboard.TargetProperty="Opacity"
                    From="0.0"
                        To="1.0"
                        Duration="0:0:0.2"
                    BeginTime="0:0:0.2"/>
            </Storyboard>

            <!-- Styles -->
            <Style x:Key="ControlButtonStyle"
                    TargetType="Button">
                <Setter Property="Background"
                        Value="Transparent"/>
                <Setter Property="Foreground"
                        Value="{StaticResource NeonGreenBrush}"/>
                <Setter Property="BorderBrush"
                        Value="{StaticResource NeonGreenBrush}"/>
                <Setter Property="BorderThickness"
                        Value="1"/>
                <Setter Property="Padding"
                        Value="12"/>
                <Setter Property="CornerRadius"
                        Value="20"/>
                <Setter Property="Width"
                        Value="50"/>
                <Setter Property="Height"
                        Value="50"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Grid>
                                <Border x:Name="ButtonBackground"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        CornerRadius="{TemplateBinding CornerRadius}">
                                    <ContentPresenter HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"/>
                                </Border>
                                <Border x:Name="GlowEffect"
                                        Background="{StaticResource NeonGreenBrush}"
                                        CornerRadius="{TemplateBinding CornerRadius}"
                                        Opacity="0">
                                    <Border.Effect>
                                        <BlurEffect Radius="20"/>
                                    </Border.Effect>
                                </Border>
                            </Grid>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsPointerOver"
                                        Value="True">
                                    <Setter TargetName="GlowEffect"
                                            Property="Opacity"
                                            Value="0.2"/>
                                </Trigger>
                                <Trigger Property="IsPressed"
                                        Value="True">
                                    <Setter TargetName="GlowEffect"
                                            Property="Opacity"
                                            Value="0.4"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Video Item Template -->
            <DataTemplate x:Key="VideoItemTemplate">
                <Grid Margin="0,4"
                        Height="72">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="128"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Thumbnail -->
                    <Image Source="{Binding ThumbnailUrl}"
                           Stretch="UniformToFill"
                           CornerRadius="6"/>

                    <!-- Video Info -->
                    <StackPanel Grid.Column="1"
                            Margin="12,0,0,0">
                        <TextBlock Text="{Binding Title}"
                                   FontSize="14"
                                   TextWrapping="NoWrap"
                                   TextTrimming="CharacterEllipsis"
                                   Foreground="White"/>
                        <TextBlock Text="{Binding Author}"
                                   FontSize="12"
                                   Opacity="0.7"
                                   Margin="0,4,0,0"
                                   Foreground="White"/>
                    </StackPanel>

                    <!-- Playing Indicator -->
                    <Border x:Name="PlayingIndicator"
                            Grid.ColumnSpan="2"
                            Background="{StaticResource NeonGreenBrush}"
                            Opacity="0.1"
                            CornerRadius="6"
                            Visibility="{Binding IsCurrentlyPlaying}"/>
                </Grid>
            </DataTemplate>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Background="{StaticResource BackgroundBrush}">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="300"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Main Video Area -->
        <Border Grid.Column="0"
                Background="{StaticResource SurfaceBrush}"
                CornerRadius="12"
                Margin="12">
            <Grid>
                <MediaPlayerElement x:Name="VideoPlayer"
                                    AreTransportControlsEnabled="False"
                                    Stretch="Uniform"/>

                <!-- Video Gradient Overlay -->
                <Rectangle x:Name="VideoGradient"
                           Opacity="0.3">
                    <Rectangle.Fill>
                        <LinearGradientBrush StartPoint="0,0"
                                EndPoint="1,1">
                            <GradientStop Color="#00000000"
                                    Offset="0"/>
                            <GradientStop Color="{x:Bind VideoAverageColor, Mode=OneWay}"
                                    Offset="1"/>
                        </LinearGradientBrush>
                    </Rectangle.Fill>
                </Rectangle>
            </Grid>
        </Border>

        <!-- Sidebar -->
        <Border Grid.Column="1"
                Grid.RowSpan="2"
                Background="{StaticResource SurfaceBrush}"
                CornerRadius="12"
                Margin="0,12,12,12">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Search Bar -->
                <TextBox Grid.Row="0"
                         x:Name="SearchBox"
                         PlaceholderText="Search videos..."
                         Text="{x:Bind SearchQuery, Mode=TwoWay}"
                         Margin="12"
                         Background="#FF2D2D2D"
                         Foreground="White">
                    <TextBox.KeyboardAccelerators>
                        <KeyboardAccelerator Key="F"
                                Modifiers="Control"/>
                    </TextBox.KeyboardAccelerators>
                </TextBox>

                <!-- Playlist -->
                <ListView x:Name="PlaylistListView"
                          Grid.Row="1"
                          ItemTemplate="{StaticResource VideoItemTemplate}"
                          ItemsSource="{x:Bind FilteredVideos}"
                          Background="Transparent"
                          SelectionMode="Single"
                          IsItemClickEnabled="True"
                          ItemClick="OnVideoItemClick"
                          Margin="12,0,12,12"/>
            </Grid>
        </Border>

        <!-- Controls -->
        <StackPanel Grid.Row="1"
                    Grid.Column="0"
                    Orientation="Horizontal"
                    HorizontalAlignment="Center"
                    Margin="12">
            <Button Style="{StaticResource ControlButtonStyle}"
                    Click="PreviousVideoCommand"
                    Margin="4,0">
                <FontIcon FontFamily="Segoe MDL2 Assets"
                        Glyph="&#xE892;"/>
            </Button>
            <Button Style="{StaticResource ControlButtonStyle}"
                    Click="PlayPauseCommand"
                    Margin="4,0">
                <FontIcon x:Name="PlayPauseIcon"
                          FontFamily="Segoe MDL2 Assets"
                          Glyph="{x:Bind IsPlaying ? '&#xE769;' : '&#xE768;', Mode=OneWay}"/>
            </Button>
            <Button Style="{StaticResource ControlButtonStyle}"
                    Click="NextVideoCommand"
                    Margin="4,0">
                <FontIcon FontFamily="Segoe MDL2 Assets"
                        Glyph="&#xE893;"/>
            </Button>
            <Button Style="{StaticResource ControlButtonStyle}"
                    Click="ShuffleCommand"
                    Margin="4,0">
                <FontIcon FontFamily="Segoe MDL2 Assets"
                        Glyph="&#xE8B1;"/>
            </Button>
            <Button Style="{StaticResource ControlButtonStyle}"
                    Click="ToggleLoopCommand"
                    Margin="4,0">
                <Grid>
                    <FontIcon FontFamily="Segoe MDL2 Assets"
                            Glyph="&#xE8EE;"/>
                    <TextBlock Text="{x:Bind LoopCount, Mode=OneWay}"
                               Visibility="{x:Bind LoopCount > 0 ? Visibility.Visible : Visibility.Collapsed, Mode=OneWay}"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               FontSize="12"/>
                </Grid>
            </Button>
        </StackPanel>
    </Grid>
</Window>