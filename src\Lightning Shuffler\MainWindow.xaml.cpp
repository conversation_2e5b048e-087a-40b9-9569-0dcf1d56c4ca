#include "pch.h"
#include "MainWindow.xaml.h"
#include "VideoItem.h"
#include "PlaylistItem.h"
#if __has_include("MainWindow.g.cpp")
#include "MainWindow.g.cpp"
#endif

using namespace winrt;
using namespace Microsoft::UI::Xaml;
using namespace Microsoft::UI::Xaml::Controls;
using namespace Windows::Media::Playback;
using namespace Windows::Media::Core;
using namespace Windows::Foundation;
using namespace Windows::UI;

namespace winrt::Lightning_Shuffler::implementation
{
    MainWindow::MainWindow()
    {
        InitializeComponent();
        InitializeMediaPlayer();
        m_filteredVideosObservable = winrt::single_threaded_observable_vector<winrt::Lightning_Shuffler::VideoItem>();
        UpdateLoopCountDisplay();
        UpdatePlayPauseIcon();
    }

    bool MainWindow::IsPlaying() const
    {
        return m_isPlaying;
    }

    void MainWindow::IsPlaying(bool value)
    {
        m_isPlaying = value;
    }

    int32_t MainWindow::LoopCount() const
    {
        return m_loopCount;
    }

    void MainWindow::LoopCount(int32_t value)
    {
        m_loopCount = value;
    }

    hstring MainWindow::CurrentVideoTitle()
    {
        return m_currentVideoTitle;
    }

    hstring MainWindow::SearchQuery()
    {
        return m_searchQuery;
    }

    Windows::Foundation::Collections::IObservableVector<winrt::Lightning_Shuffler::VideoItem> MainWindow::FilteredVideos()
    {
        return m_filteredVideosObservable;
    }

    Windows::UI::Color MainWindow::VideoAverageColor() const
    {
        return m_videoAverageColor;
    }

    void MainWindow::SearchQuery(hstring const &value)
    {
        m_searchQuery = value;
        // Filter videos based on search query
        if (!m_currentQueue.empty())
        {
            auto query = std::wstring(value);
            std::transform(query.begin(), query.end(), query.begin(), ::tolower);

            m_filteredVideos.clear();
            m_filteredVideosObservable.Clear();

            for (const auto &video : m_currentQueue)
            {
                auto title = std::wstring(video.Title);
                auto author = std::wstring(video.Author);
                std::transform(title.begin(), title.end(), title.begin(), ::tolower);
                std::transform(author.begin(), author.end(), author.begin(), ::tolower);

                if (title.find(query) != std::wstring::npos ||
                    author.find(query) != std::wstring::npos)
                {
                    m_filteredVideos.push_back(video);
                    // Create VideoItem for the observable vector
                    auto videoItem = winrt::make<implementation::VideoItem>(
                        hstring(video.Id), hstring(video.Title), hstring(video.Author), hstring(video.ThumbnailUrl));
                    m_filteredVideosObservable.Append(videoItem);
                }
            }
        }
    }

    void MainWindow::PlayPauseCommand()
    {
        if (m_mediaPlayer)
        {
            if (m_isPlaying)
            {
                m_mediaPlayer.Pause();
            }
            else
            {
                m_mediaPlayer.Play();
            }
            m_isPlaying = !m_isPlaying;
            UpdateMediaControls();
            UpdatePlayPauseIcon();
        }
    }

    void MainWindow::NextVideoCommand()
    {
        if (!m_currentQueue.empty())
        {
            m_currentVideoIndex = (m_currentVideoIndex + 1) % m_currentQueue.size();
            PlayVideo(m_currentQueue[m_currentVideoIndex]);
        }
    }

    void MainWindow::PreviousVideoCommand()
    {
        if (!m_currentQueue.empty())
        {
            if (m_currentVideoIndex == 0)
            {
                m_currentVideoIndex = m_currentQueue.size() - 1;
            }
            else
            {
                m_currentVideoIndex--;
            }
            PlayVideo(m_currentQueue[m_currentVideoIndex]);
        }
    }

    void MainWindow::ShuffleCommand()
    {
        if (!m_currentQueue.empty())
        {
            auto& currentVideo = m_currentQueue[m_currentVideoIndex];

            // Start shuffle animation
            StartShuffleAnimation();

            // Shuffle the queue
            std::random_device rd;
            std::mt19937 gen(rd());
            std::shuffle(m_currentQueue.begin(), m_currentQueue.end(), gen);

            // Find the new index of the current video
            auto it = std::find_if(m_currentQueue.begin(), m_currentQueue.end(),
                                   [&currentVideo](const Video &v)
                                   { return v.Id == currentVideo.Id; });
            m_currentVideoIndex = std::distance(m_currentQueue.begin(), it);

            // Update observable vector
            UpdateFilteredVideos();
        }
    }

    void MainWindow::ToggleLoopCommand()
    {
        m_loopCount = (m_loopCount + 1) % 100; // Max 99 loops
        UpdateLoopCountDisplay();
    }

    void MainWindow::AddPlaylistCommand(hstring const &url)
    {
        LoadPlaylistAsync(url);
    }

    void MainWindow::OnVideoItemClick(IInspectable const &, Controls::ItemClickEventArgs const &e)
    {
        if (auto videoItem = e.ClickedItem().try_as<winrt::Lightning_Shuffler::VideoItem>())
        {
            auto videoId = std::wstring(videoItem.Id());
            auto it = std::find_if(m_currentQueue.begin(), m_currentQueue.end(),
                                   [&videoId](const Video &v)
                                   { return v.Id == videoId; });
            if (it != m_currentQueue.end())
            {
                m_currentVideoIndex = std::distance(m_currentQueue.begin(), it);
                PlayVideo(*it);
            }
        }
    }

    IAsyncAction MainWindow::LoadPlaylistAsync(hstring const &url)
    {
        try
        {
            auto playlist = co_await m_youtubeService.FetchPlaylistAsync(std::wstring(url));
            m_playlists.push_back(playlist);
            UpdateQueue();

            if (m_currentQueue.empty())
            {
                m_currentVideoIndex = 0;
                PlayVideo(m_currentQueue[0]);
            }
        }
        catch (...)
        {
            // TODO: Show error message to user
        }
    }

    void MainWindow::UpdateQueue()
    {
        m_currentQueue.clear();
        for (const auto &playlist : m_playlists)
        {
            auto videos = playlist.Videos();
            for (auto const &videoItem : videos)
            {
                Video video;
                video.Id = std::wstring(videoItem.Id());
                video.Title = std::wstring(videoItem.Title());
                video.Author = std::wstring(videoItem.Author());
                video.ThumbnailUrl = std::wstring(videoItem.ThumbnailUrl());
                m_currentQueue.push_back(video);
            }
        }

        // Update observable vector
        UpdateFilteredVideos();
    }

    void MainWindow::UpdateFilteredVideos()
    {
        m_filteredVideosObservable.Clear();
        for (const auto &video : m_currentQueue)
        {
            auto videoItem = winrt::make<implementation::VideoItem>(
                hstring(video.Id), hstring(video.Title), hstring(video.Author), hstring(video.ThumbnailUrl));
            m_filteredVideosObservable.Append(videoItem);
        }
    }

    void MainWindow::PlayVideo(const Video &video)
    {
        // Create video URL
        auto videoUrl = L"https://www.youtube.com/watch?v=" + video.Id;
        m_mediaPlayer.Source(MediaSource::CreateFromUri(Uri(videoUrl)));
        m_mediaPlayer.Play();
        m_isPlaying = true;
        m_currentVideoTitle = hstring(video.Title);

        // Update video color
        UpdateVideoColorAsync(video);

        UpdateMediaControls();
        UpdatePlayPauseIcon();
    }

    IAsyncAction MainWindow::UpdateVideoColorAsync(const Video &video)
    {
        try
        {
            // TODO: Implement video thumbnail color extraction using video.ThumbnailUrl
            // For now, use a placeholder color
            UNREFERENCED_PARAMETER(video);
            m_videoAverageColor = Colors::DarkSlateBlue();

            // Update gradient
            VideoGradient().Opacity(0.3);
        }
        catch (...)
        {
            // Fallback to default color
            m_videoAverageColor = Colors::Transparent();
        }
    }

    void MainWindow::StartShuffleAnimation()
    {
        // Create shuffle animation programmatically for WinUI 3
        if (auto listView = PlaylistListView())
        {
            // Create fade out animation
            auto fadeOutAnimation = Media::Animation::DoubleAnimation();
            fadeOutAnimation.From(1.0);
            fadeOutAnimation.To(0.0);
            fadeOutAnimation.Duration(Microsoft::UI::Xaml::DurationHelper::FromTimeSpan(Windows::Foundation::TimeSpan(std::chrono::milliseconds(200))));

            // Create fade in animation
            auto fadeInAnimation = Media::Animation::DoubleAnimation();
            fadeInAnimation.From(0.0);
            fadeInAnimation.To(1.0);
            fadeInAnimation.Duration(Microsoft::UI::Xaml::DurationHelper::FromTimeSpan(Windows::Foundation::TimeSpan(std::chrono::milliseconds(200))));
            fadeInAnimation.BeginTime(Windows::Foundation::TimeSpan(std::chrono::milliseconds(200)));

            // Create storyboard
            auto storyboard = Media::Animation::Storyboard();
            storyboard.Children().Append(fadeOutAnimation);
            storyboard.Children().Append(fadeInAnimation);

            // Set targets
            Media::Animation::Storyboard::SetTarget(fadeOutAnimation, listView);
            Media::Animation::Storyboard::SetTargetProperty(fadeOutAnimation, L"Opacity");
            Media::Animation::Storyboard::SetTarget(fadeInAnimation, listView);
            Media::Animation::Storyboard::SetTargetProperty(fadeInAnimation, L"Opacity");

            // Start animation
            storyboard.Begin();
        }
    }

    void MainWindow::InitializeMediaPlayer()
    {
        m_mediaPlayer = MediaPlayer();
        m_mediaPlayer.AutoPlay(true);

        // Connect the media player to the UI element
        VideoPlayer().SetMediaPlayer(m_mediaPlayer);

        m_mediaPlayer.MediaEnded([this](const MediaPlayer &, const IInspectable &)
                                 {
            if (m_loopCount > 0)
            {
                m_loopCount--;
                m_mediaPlayer.Position(TimeSpan(0));
                m_mediaPlayer.Play();
            }
            else
            {
                NextVideoCommand();
            } });
    }

    void MainWindow::UpdateMediaControls()
    {
        // Update system media controls
        auto mediaControls = m_mediaPlayer.SystemMediaTransportControls();
        mediaControls.IsEnabled(true);
        mediaControls.IsPlayEnabled(true);
        mediaControls.IsPauseEnabled(true);
        mediaControls.IsNextEnabled(true);
        mediaControls.IsPreviousEnabled(true);

        auto props = mediaControls.DisplayUpdater();
        props.Type(Windows::Media::MediaPlaybackType::Video);
        props.VideoProperties().Title(m_currentVideoTitle);
        props.Update();
    }

    void MainWindow::UpdatePlayPauseIcon()
    {
        if (auto playPauseIcon = PlayPauseIcon())
        {
            // &#xE768; = Play icon, &#xE769; = Pause icon
            playPauseIcon.Glyph(m_isPlaying ? L"\uE769" : L"\uE768");
        }
    }

    void MainWindow::UpdateLoopCountDisplay()
    {
        if (auto loopCountText = LoopCountText())
        {
            if (m_loopCount > 0)
            {
                loopCountText.Text(winrt::to_hstring(m_loopCount));
                loopCountText.Visibility(Visibility::Visible);
            }
            else
            {
                loopCountText.Visibility(Visibility::Collapsed);
            }
        }
    }
}