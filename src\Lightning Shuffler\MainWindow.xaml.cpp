#include "pch.h"
#include "MainWindow.xaml.h"
#if __has_include("MainWindow.g.cpp")
#include "MainWindow.g.cpp"
#endif

using namespace winrt;
using namespace Microsoft::UI::Xaml;
using namespace Windows::Media::Playback;
using namespace Windows::Media::Core;
using namespace Windows::Foundation;
using namespace Windows::UI;

namespace winrt::Lightning_Shuffler::implementation
{
    MainWindow::MainWindow()
    {
        InitializeComponent();
        InitializeMediaPlayer();
    }

    bool MainWindow::IsPlaying()
    {
        return m_isPlaying;
    }

    void MainWindow::IsPlaying(bool value)
    {
        m_isPlaying = value;
    }

    int32_t MainWindow::LoopCount()
    {
        return m_loopCount;
    }

    void MainWindow::LoopCount(int32_t value)
    {
        m_loopCount = value;
    }

    hstring MainWindow::CurrentVideoTitle()
    {
        return m_currentVideoTitle;
    }

    hstring MainWindow::SearchQuery()
    {
        return m_searchQuery;
    }

    void MainWindow::SearchQuery(hstring const& value)
    {
        m_searchQuery = value;
        // Filter videos based on search query
        if (!m_currentQueue.empty())
        {
            auto query = std::wstring(value);
            std::transform(query.begin(), query.end(), query.begin(), ::tolower);
            
            m_filteredVideos.clear();
            for (const auto& video : m_currentQueue)
            {
                auto title = std::wstring(video.Title);
                auto author = std::wstring(video.Author);
                std::transform(title.begin(), title.end(), title.begin(), ::tolower);
                std::transform(author.begin(), author.end(), author.begin(), ::tolower);
                
                if (title.find(query) != std::wstring::npos ||
                    author.find(query) != std::wstring::npos)
                {
                    m_filteredVideos.push_back(video);
                }
            }
            
            // Update ListView binding
            PlaylistListView().ItemsSource(winrt::single_threaded_observable_vector<Video>(
                std::move(m_filteredVideos)));
        }
    }

    void MainWindow::PlayPauseCommand()
    {
        if (m_mediaPlayer)
        {
            if (m_isPlaying)
            {
                m_mediaPlayer.Pause();
            }
            else
            {
                m_mediaPlayer.Play();
            }
            m_isPlaying = !m_isPlaying;
            UpdateMediaControls();
        }
    }

    void MainWindow::NextVideoCommand()
    {
        if (!m_currentQueue.empty())
        {
            m_currentVideoIndex = (m_currentVideoIndex + 1) % m_currentQueue.size();
            PlayVideo(m_currentQueue[m_currentVideoIndex]);
        }
    }

    void MainWindow::PreviousVideoCommand()
    {
        if (!m_currentQueue.empty())
        {
            if (m_currentVideoIndex == 0)
            {
                m_currentVideoIndex = m_currentQueue.size() - 1;
            }
            else
            {
                m_currentVideoIndex--;
            }
            PlayVideo(m_currentQueue[m_currentVideoIndex]);
        }
    }

    void MainWindow::ShuffleCommand()
    {
        if (!m_currentQueue.empty())
        {
            auto currentVideo = m_currentQueue[m_currentVideoIndex];
            
            // Start shuffle animation
            StartShuffleAnimation();
            
            // Shuffle the queue
            std::random_device rd;
            std::mt19937 gen(rd());
            std::shuffle(m_currentQueue.begin(), m_currentQueue.end(), gen);
            
            // Find the new index of the current video
            auto it = std::find_if(m_currentQueue.begin(), m_currentQueue.end(),
                [&currentVideo](const Video& v) { return v.Id == currentVideo.Id; });
            m_currentVideoIndex = std::distance(m_currentQueue.begin(), it);
            
            // Update ListView
            PlaylistListView().ItemsSource(winrt::single_threaded_observable_vector<Video>(
                std::move(m_currentQueue)));
        }
    }

    void MainWindow::ToggleLoopCommand()
    {
        m_loopCount = (m_loopCount + 1) % 100; // Max 99 loops
    }

    void MainWindow::AddPlaylistCommand(hstring const& url)
    {
        LoadPlaylistAsync(url);
    }

    void MainWindow::OnVideoItemClick(IInspectable const&, Controls::ItemClickEventArgs const& e)
    {
        if (auto video = e.ClickedItem().try_as<Video>())
        {
            auto it = std::find_if(m_currentQueue.begin(), m_currentQueue.end(),
                [&video](const Video& v) { return v.Id == video.Id; });
            if (it != m_currentQueue.end())
            {
                m_currentVideoIndex = std::distance(m_currentQueue.begin(), it);
                PlayVideo(*it);
            }
        }
    }

    IAsyncAction MainWindow::LoadPlaylistAsync(hstring const& url)
    {
        try
        {
            auto playlist = co_await m_youtubeService.FetchPlaylistAsync(std::wstring(url));
            m_playlists.push_back(playlist);
            UpdateQueue();
            
            if (m_currentQueue.empty())
            {
                m_currentVideoIndex = 0;
                PlayVideo(m_currentQueue[0]);
            }
        }
        catch (...)
        {
            // TODO: Show error message to user
        }
    }

    void MainWindow::UpdateQueue()
    {
        m_currentQueue.clear();
        for (const auto& playlist : m_playlists)
        {
            m_currentQueue.insert(m_currentQueue.end(),
                playlist.Videos.begin(), playlist.Videos.end());
        }
        
        // Update ListView
        PlaylistListView().ItemsSource(winrt::single_threaded_observable_vector<Video>(
            std::move(m_currentQueue)));
    }

    void MainWindow::PlayVideo(const Video& video)
    {
        // Create video URL
        auto videoUrl = L"https://www.youtube.com/watch?v=" + video.Id;
        m_mediaPlayer.Source(MediaSource::CreateFromUri(Uri(videoUrl)));
        m_mediaPlayer.Play();
        m_isPlaying = true;
        m_currentVideoTitle = hstring(video.Title);
        
        // Update video color
        UpdateVideoColorAsync(video);
        
        UpdateMediaControls();
    }

    IAsyncAction MainWindow::UpdateVideoColorAsync(const Video& video)
    {
        try
        {
            // TODO: Implement video thumbnail color extraction
            // For now, use a placeholder color
            m_videoAverageColor = Colors::DarkSlateBlue();
            
            // Update gradient
            VideoGradient().Opacity(0.3);
        }
        catch (...)
        {
            // Fallback to default color
            m_videoAverageColor = Colors::Transparent();
        }
    }

    void MainWindow::StartShuffleAnimation()
    {
        if (auto storyboard = Resources().Lookup(box_value(L"ShuffleAnimation")).try_as<Media::Animation::Storyboard>())
        {
            storyboard.Begin();
        }
    }

    void MainWindow::InitializeMediaPlayer()
    {
        m_mediaPlayer = MediaPlayer();
        m_mediaPlayer.AutoPlay(true);
        
        m_mediaPlayer.MediaEnded([this](const MediaPlayer&, const IInspectable&) {
            if (m_loopCount > 0)
            {
                m_loopCount--;
                m_mediaPlayer.Position(TimeSpan(0));
                m_mediaPlayer.Play();
            }
            else
            {
                NextVideoCommand();
            }
        });
    }

    void MainWindow::UpdateMediaControls()
    {
        // Update system media controls
        auto mediaControls = m_mediaPlayer.SystemMediaTransportControls();
        mediaControls.IsEnabled(true);
        mediaControls.IsPlayEnabled(true);
        mediaControls.IsPauseEnabled(true);
        mediaControls.IsNextEnabled(true);
        mediaControls.IsPreviousEnabled(true);
        
        auto props = mediaControls.DisplayUpdater();
        props.Type(Windows::Media::MediaPlaybackType::Video);
        props.VideoProperties().Title(m_currentVideoTitle);
        props.Update();
    }
}