#pragma once
#include "MainWindow.g.h"
#include "YouTubeService.h"
#include <winrt/Windows.Media.Playback.h>
#include <winrt/Windows.Media.Core.h>
#include <winrt/Windows.Foundation.Collections.h>
#include <winrt/Windows.UI.h>

namespace winrt::Lightning_Shuffler::implementation
{
    struct MainWindow : MainWindowT<MainWindow>
    {
        MainWindow();

        bool IsPlaying();
        void IsPlaying(bool value);
        int32_t LoopCount();
        void LoopCount(int32_t value);
        hstring CurrentVideoTitle();
        hstring SearchQuery();
        void SearchQuery(hstring const& value);

        void PlayPauseCommand();
        void NextVideoCommand();
        void PreviousVideoCommand();
        void ShuffleCommand();
        void ToggleLoopCommand();
        void AddPlaylistCommand(hstring const& url);
        void OnVideoItemClick(Windows::Foundation::IInspectable const& sender, Microsoft::UI::Xaml::Controls::ItemClickEventArgs const& e);

    private:
        bool m_isPlaying{ false };
        int32_t m_loopCount{ 0 };
        hstring m_currentVideoTitle;
        hstring m_searchQuery;
        Windows::UI::Color m_videoAverageColor{ Windows::UI::Colors::Transparent() };
        
        // Media player
        Windows::Media::Playback::MediaPlayer m_mediaPlayer{ nullptr };
        
        // YouTube service
        YouTubeService m_youtubeService;
        std::vector<Playlist> m_playlists;
        std::vector<Video> m_currentQueue;
        std::vector<Video> m_filteredVideos;
        size_t m_currentVideoIndex{ 0 };
        
        void InitializeMediaPlayer();
        void UpdateMediaControls();
        Windows::Foundation::IAsyncAction LoadPlaylistAsync(hstring const& url);
        void UpdateQueue();
        void PlayVideo(const Video& video);
        Windows::Foundation::IAsyncAction UpdateVideoColorAsync(const Video& video);
        void StartShuffleAnimation();
    };
}

namespace winrt::Lightning_Shuffler::factory_implementation
{
    struct MainWindow : MainWindowT<MainWindow, implementation::MainWindow>
    {
    };
}