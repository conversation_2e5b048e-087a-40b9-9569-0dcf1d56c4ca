#include "pch.h"
#include "PlaylistItem.h"
#if __has_include("PlaylistItem.g.cpp")
#include "PlaylistItem.g.cpp"
#endif

using namespace winrt;
using namespace Windows::Foundation::Collections;

namespace winrt::Lightning_Shuffler::implementation
{
    PlaylistItem::PlaylistItem(hstring const& id, hstring const& title)
        : m_id(id), m_title(title)
    {
        m_videos = winrt::single_threaded_vector<Lightning_Shuffler::VideoItem>();
    }

    hstring PlaylistItem::Id()
    {
        return m_id;
    }

    void PlaylistItem::Id(hstring const& value)
    {
        m_id = value;
    }

    hstring PlaylistItem::Title()
    {
        return m_title;
    }

    void PlaylistItem::Title(hstring const& value)
    {
        m_title = value;
    }

    IVector<Lightning_Shuffler::VideoItem> PlaylistItem::Videos()
    {
        if (!m_videos)
        {
            m_videos = winrt::single_threaded_vector<Lightning_Shuffler::VideoItem>();
        }
        return m_videos;
    }

    void PlaylistItem::Videos(IVector<Lightning_Shuffler::VideoItem> const& value)
    {
        m_videos = value;
    }
}
