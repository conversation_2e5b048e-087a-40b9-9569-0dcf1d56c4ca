#pragma once
#include "PlaylistItem.g.h"
#include "VideoItem.h"

namespace winrt::Lightning_Shuffler::implementation
{
    struct PlaylistItem : PlaylistItemT<PlaylistItem>
    {
        PlaylistItem() = default;
        PlaylistItem(hstring const& id, hstring const& title);

        hstring Id();
        void Id(hstring const& value);
        hstring Title();
        void Title(hstring const& value);
        Windows::Foundation::Collections::IVector<Lightning_Shuffler::VideoItem> Videos();
        void Videos(Windows::Foundation::Collections::IVector<Lightning_Shuffler::VideoItem> const& value);

    private:
        hstring m_id;
        hstring m_title;
        Windows::Foundation::Collections::IVector<Lightning_Shuffler::VideoItem> m_videos{ nullptr };
    };
}

namespace winrt::Lightning_Shuffler::factory_implementation
{
    struct PlaylistItem : PlaylistItemT<PlaylistItem, implementation::PlaylistItem>
    {
    };
}
