#include "pch.h"
#include "VideoItem.h"
#if __has_include("VideoItem.g.cpp")
#include "VideoItem.g.cpp"
#endif

using namespace winrt;

namespace winrt::Lightning_Shuffler::implementation
{
    VideoItem::VideoItem(hstring const& id, hstring const& title, hstring const& author, hstring const& thumbnailUrl)
        : m_id(id), m_title(title), m_author(author), m_thumbnailUrl(thumbnailUrl)
    {
    }

    hstring VideoItem::Id()
    {
        return m_id;
    }

    void VideoItem::Id(hstring const& value)
    {
        m_id = value;
    }

    hstring VideoItem::Title()
    {
        return m_title;
    }

    void VideoItem::Title(hstring const& value)
    {
        m_title = value;
    }

    hstring VideoItem::Author()
    {
        return m_author;
    }

    void VideoItem::Author(hstring const& value)
    {
        m_author = value;
    }

    hstring VideoItem::ThumbnailUrl()
    {
        return m_thumbnailUrl;
    }

    void VideoItem::ThumbnailUrl(hstring const& value)
    {
        m_thumbnailUrl = value;
    }

    bool VideoItem::IsCurrentlyPlaying()
    {
        return m_isCurrentlyPlaying;
    }

    void VideoItem::IsCurrentlyPlaying(bool value)
    {
        m_isCurrentlyPlaying = value;
    }
}
