#pragma once
#include "VideoItem.g.h"

namespace winrt::Lightning_Shuffler::implementation
{
    struct VideoItem : VideoItemT<VideoItem>
    {
        VideoItem() = default;
        VideoItem(hstring const& id, hstring const& title, hstring const& author, hstring const& thumbnailUrl);

        hstring Id();
        void Id(hstring const& value);
        hstring Title();
        void Title(hstring const& value);
        hstring Author();
        void Author(hstring const& value);
        hstring ThumbnailUrl();
        void ThumbnailUrl(hstring const& value);
        bool IsCurrentlyPlaying();
        void IsCurrentlyPlaying(bool value);

    private:
        hstring m_id;
        hstring m_title;
        hstring m_author;
        hstring m_thumbnailUrl;
        bool m_isCurrentlyPlaying{ false };
    };
}

namespace winrt::Lightning_Shuffler::factory_implementation
{
    struct VideoItem : VideoItemT<VideoItem, implementation::VideoItem>
    {
    };
}
