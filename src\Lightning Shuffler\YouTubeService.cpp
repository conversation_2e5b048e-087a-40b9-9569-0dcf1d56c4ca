#include "pch.h"
#include "YouTubeService.h"
#include "VideoItem.h"
#include "PlaylistItem.h"
#include <regex>

using namespace winrt;
using namespace Windows::Data::Json;
using namespace Windows::Web::Http;
using namespace Windows::Foundation;

namespace winrt::<PERSON>_Shuffler
{
    YouTubeService::YouTubeService()
    {
        m_httpClient = HttpClient();
    }

    IAsyncOperation<winrt::Lightning_Shuffler::PlaylistItem> YouTubeService::FetchPlaylistAsync(std::wstring const &url)
    {
        auto playlistId = ExtractPlaylistId(url);
        auto apiUrl = L"https://www.googleapis.com/youtube/v3/playlistItems?"
                      L"part=snippet&maxResults=50&playlistId=" +
                      playlistId +
                      L"&key=" + API_KEY;

        auto response = co_await m_httpClient.GetStringAsync(Uri(hstring(apiUrl)));
        auto jsonObject = JsonObject::Parse(response);
        auto items = jsonObject.GetNamedArray(L"items");

        auto playlist = winrt::make<implementation::PlaylistItem>(hstring(playlistId), L"");
        auto videos = playlist.Videos();

        for (auto const &item : items)
        {
            auto itemObj = item.GetObject();
            auto snippet = itemObj.GetNamedObject(L"snippet");
            videos.Append(ParseVideoJson(snippet));
        }

        co_return playlist;
    }

    IAsyncOperation<Windows::Foundation::Collections::IVector<winrt::Lightning_Shuffler::VideoItem>> YouTubeService::SearchVideosAsync(std::wstring const &query)
    {
        auto results = winrt::single_threaded_vector<winrt::Lightning_Shuffler::VideoItem>();
        if (query.empty())
            co_return results;

        // Filter videos based on local data instead of making API calls
        // TODO: Implement local search logic

        co_return results;
    }

    std::wstring YouTubeService::ExtractPlaylistId(std::wstring const &url)
    {
        std::wregex playlistRegex(L"list=([\\w-]+)");
        std::wsmatch match;
        if (std::regex_search(url, match, playlistRegex))
        {
            return match[1].str();
        }
        return L"";
    }

    winrt::Lightning_Shuffler::VideoItem YouTubeService::ParseVideoJson(JsonObject const &json)
    {
        auto resourceId = json.GetNamedObject(L"resourceId");
        auto videoId = resourceId.GetNamedString(L"videoId");
        auto title = json.GetNamedString(L"title");
        auto author = json.GetNamedString(L"videoOwnerChannelTitle");

        auto thumbnails = json.GetNamedObject(L"thumbnails");
        auto defaultThumb = thumbnails.GetNamedObject(L"default");
        auto thumbnailUrl = defaultThumb.GetNamedString(L"url");

        return winrt::make<implementation::VideoItem>(videoId, title, author, thumbnailUrl);
    }
}