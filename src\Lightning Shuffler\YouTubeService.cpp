#include "pch.h"
#include "YouTubeService.h"
#include <regex>

using namespace winrt;
using namespace Windows::Data::Json;
using namespace Windows::Web::Http;

namespace winrt::Lightning_Shuffler
{
    YouTubeService::YouTubeService()
    {
        m_httpClient = HttpClient();
    }

    Windows::Foundation::IAsyncOperation<Playlist> YouTubeService::FetchPlaylistAsync(std::wstring const& url)
    {
        auto playlistId = ExtractPlaylistId(url);
        auto apiUrl = L"https://www.googleapis.com/youtube/v3/playlistItems?"
            L"part=snippet&maxResults=50&playlistId=" + playlistId +
            L"&key=" + API_KEY;

        auto response = co_await m_httpClient.GetStringAsync(Uri(apiUrl));
        auto jsonObject = JsonObject::Parse(response);
        auto items = jsonObject.GetNamedArray(L"items");

        Playlist playlist;
        playlist.Id = playlistId;

        for (auto const& item : items)
        {
            auto itemObj = item.GetObject();
            auto snippet = itemObj.GetNamedObject(L"snippet");
            playlist.Videos.push_back(ParseVideoJson(snippet));
        }

        co_return playlist;
    }

    Windows::Foundation::IAsyncOperation<std::vector<Video>> YouTubeService::SearchVideosAsync(std::wstring const& query)
    {
        std::vector<Video> results;
        if (query.empty()) co_return results;

        // Filter videos based on local data instead of making API calls
        // TODO: Implement local search logic

        co_return results;
    }

    std::wstring YouTubeService::ExtractPlaylistId(std::wstring const& url)
    {
        std::wregex playlistRegex(L"list=([\\w-]+)");
        std::wsmatch match;
        if (std::regex_search(url, match, playlistRegex))
        {
            return match[1].str();
        }
        return L"";
    }

    Video YouTubeService::ParseVideoJson(JsonObject const& json)
    {
        Video video;
        video.Id = json.GetNamedString(L"resourceId").GetNamedString(L"videoId");
        video.Title = json.GetNamedString(L"title");
        video.Author = json.GetNamedString(L"videoOwnerChannelTitle");
        
        auto thumbnails = json.GetNamedObject(L"thumbnails");
        auto defaultThumb = thumbnails.GetNamedObject(L"default");
        video.ThumbnailUrl = defaultThumb.GetNamedString(L"url");

        return video;
    }
}