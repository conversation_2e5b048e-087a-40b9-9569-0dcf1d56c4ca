#pragma once
#include <winrt/Windows.Foundation.Collections.h>
#include <winrt/Windows.Data.Json.h>
#include <winrt/Windows.Web.Http.h>
#include <string>
#include <vector>

namespace winrt::Lightning_Shuffler
{
    struct Video
    {
        std::wstring Id;
        std::wstring Title;
        std::wstring Author;
        std::wstring ThumbnailUrl;
    };

    struct Playlist
    {
        std::wstring Id;
        std::wstring Title;
        std::vector<Video> Videos;
    };

    class YouTubeService
    {
    public:
        YouTubeService();

        Windows::Foundation::IAsyncOperation<Lightning_Shuffler::PlaylistItem> FetchPlaylistAsync(std::wstring const &url);
        Windows::Foundation::IAsyncOperation<Windows::Foundation::Collections::IVector<Lightning_Shuffler::VideoItem>> SearchVideosAsync(std::wstring const &query);

    private:
        const std::wstring API_KEY = L"AIzaSyAFGB8-5IffhA-sAtvt7MYQJLwQJZTPypI";
        Windows::Web::Http::HttpClient m_httpClient;

        std::wstring ExtractPlaylistId(std::wstring const &url);
        Lightning_Shuffler::VideoItem ParseVideoJson(Windows::Data::Json::JsonObject const &json);
    };
}